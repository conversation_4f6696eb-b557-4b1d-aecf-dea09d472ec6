import spotipy
from spotipy.oauth2 import SpotifyOAuth
import os

# Optional: Load from environment variables
CLIENT_ID = os.getenv("SPOTIFY_CLIENT_ID") or "515d38f826574b7b881ffc8688eb88bb"
CLIENT_SECRET = os.getenv("SPOTIFY_CLIENT_SECRET") or "6a657faddbba4cbb9639174dd825ee02"
REDIRECT_URI = "http://localhost:8888/callback"

# Required Spotify scopes
SCOPE = "user-read-playback-state user-modify-playback-state user-read-currently-playing"

sp = spotipy.Spotify(auth_manager=SpotifyOAuth(
    client_id=CLIENT_ID,
    client_secret=CLIENT_SECRET,
    redirect_uri=REDIRECT_URI,
    scope=SCOPE,
    cache_path=".spotify_cache"  # stores access/refresh tokens
))

def play_spotify_song(song_name):
    try:
        print(f"[<PERSON>] Searching for: {song_name}")
        result = sp.search(q=song_name, limit=1, type='track')
        tracks = result.get('tracks', {}).get('items', [])

        if not tracks:
            print(f"[<PERSON>] No results found for '{song_name}'")
            return

        track_uri = tracks[0]['uri']
        track_name = tracks[0]['name']
        track_artist = tracks[0]['artists'][0]['name']
        print(f"[Jarvis] Playing '{track_name}' by {track_artist}")

        devices = sp.devices()
        if not devices['devices']:
            print("[Jarvis] No active Spotify devices found. Start Spotify on a device first.")
            return

        device_id = devices['devices'][0]['id']
        sp.start_playback(device_id=device_id, uris=[track_uri])
    except Exception as e:
        print(f"[Jarvis] Error playing Spotify song: {e}")
        print("[Jarvis] Make sure Spotify is running and you're logged in.")
