from modules.discord_module import send_discord_message, call_discord_user
from modules.spotify_module import play_spotify_song
from modules.system_module import mute_volume, unmute_volume, volume_up, volume_down, lock_screen, shutdown, restart
from vosk import <PERSON><PERSON>i<PERSON>ecog<PERSON><PERSON>, Model
import pyttsx3
import pyaudio
import re
import json

engine = pyttsx3.init()
model = Model("vosk-model-small-en-us-0.15")
recognizer = Kald<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(model, 16000)
audio = pyaudio.PyAudio()

stream = audio.open(format=pyaudio.paInt16, channels=1, rate=16000, input=True, frames_per_buffer=8192)
stream.start_stream()

WAKE_WORDS = ["hey jarvis", "jarvis", "hey computer", "computer"]

def speak(text):
    engine.say(text)
    engine.runAndWait()

print("[Jarvis] - Listening...")

def listen_command():

    try:
        audio_data = stream.read(8192)

        if recognizer.AcceptWaveform(audio_data):
            result = json.loads(recognizer.Result())
            query = result['text'].lower()
            print("Heard:", query)
            return query
        else:
            return ""
    except Exception as e:
        print(f"Sorry, I can't reach the recognition service. Error: {e}")
        return ""

def handle_command(command):
    if "play" in command:
        song = command.split("play", 1)[1].strip()
        speak(f"Playing {song} on Spotify.")
        play_spotify_song(song)
    elif "send discord message to" in command:
        match = re.search(r'send discord message to (.+?) saying (.+)', command)
        if match:
            user = match.group(1).strip()
            msg = match.group(2).strip()
            send_discord_message(user, msg)
        else:
            speak("Please say the message clearly.")
    elif "mute" in command or "volume off" in command:
        mute_volume()
        speak("Volume muted.")

    elif "unmute" in command or "volume on" in command:
        unmute_volume()
        speak("Volume unmuted.")

    elif "volume up" in command:
        volume_up()
        speak("Volume increased.")

    elif "volume down" in command:
        volume_down()
        speak("Volume decreased.")

    elif "lock screen" in command or "lockdown" in command:
        speak("Locking the screen.")
        lock_screen()

    elif "shutdown" in command:
        speak("Shutting down.")
        shutdown()

    elif "restart" in command:
        speak("Restarting.")
        restart()
        
    elif "report for duty" in command:
        speak("Jarvis, at your service, sir.")

    elif "say" in command:
        message = command.split("say", 1)[1].strip()
        if message:
            speak(message)
        else:
            speak("Please say the message clearly.")
    elif "call" in command and "discord" in command:
        match = re.search(r'call (.+?) on discord', command)
        if match:
            user = match.group(1).strip()
            speak(f"Calling {user} on Discord.")
            call_discord_user(user)
        else:
            speak("Please specify who to call.")
    else:
        speak("Sorry, I don't understand that yet.")

def record_audio(seconds=30):
    print(f"[Jarvis] - Recording for {seconds} seconds...")
    frames = []
    
    # Calculate total frames based on buffer size and duration
    total_frames = int((16000 * seconds) / 8192)
    
    try:
        for i in range(total_frames):
            audio_data = stream.read(8192)
            frames.append(audio_data)
            
            # Optional: Process frames as they come in
            if recognizer.AcceptWaveform(audio_data):
                result = json.loads(recognizer.Result())
                if result.get('text'):
                    print(f"Heard: {result['text']}")
    except Exception as e:
        print(f"Error recording audio: {e}")
    
    print("[Jarvis] - Recording complete")
    return frames

def main():
    speak("Jarvis online.")
    try:
        while True:
            query = listen_command()
            if any(wake in query for wake in WAKE_WORDS):
                command = query
                for wake in WAKE_WORDS:
                    command = command.replace(wake, '').strip()
                if command:
                    handle_command(command)
    except KeyboardInterrupt:
        print("\n[Jarvis] Shutting down...")
        speak("Goodbye.")
    except Exception as e:
        print(f"[Jarvis] An error occurred: {e}")
    finally:
        # Clean up resources
        if stream:
            stream.stop_stream()
            stream.close()
        if audio:
            audio.terminate()

if __name__ == "__main__":
    main()
