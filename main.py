from modules.discord_module import *
from modules.spotify_module import *
from modules.system_module import *
from vosk import <PERSON><PERSON><PERSON><PERSON>ecog<PERSON><PERSON>, Model
import pyttsx3
import pyaudio
import re
import threading
import json

engine = pyttsx3.init()
model = Model("vosk-model-small-en-us-0.15")
recognizer = Kaldi<PERSON><PERSON>ognizer(model, 16000)
pyaudio = pyaudio.PyAduio()

WAKE_WORDS = ["hey jarvis", "jarvis"]

def speak(text):
    engine.say(text)
    engine.runAndWait()

def listen_command():
    with sr.Microphone() as source:
        print("[<PERSON>] Listening...")
        audio = recognizer.listen(source)
        
        try:
            query = recognizer.recognize_google(audio).lower()
            print("Heard:", query)
            return query
        except sr.UnknownValueError:
            return ""
        except sr.RequestError as e:
            print("Sorry, I can't reach the recognition service.")
            return ""

def handle_command(command):
    if 'play' in command:
        song = command.split("play", 1)[1].strip()
        speak(f"Playing {song} on Spotify.")
        play_spotify_song(song)
    elif 'send discord message to' in command:
        match = re.search(r'send discord message to (.+?) saying (.+)', command)
        if match:
            user = match.group(1).strip()
            msg = match.group(2).strip()
            send_discord_message(user, msg)
        else:
            speak("Please say the message clearly.")
    elif "mute" in command:
        mute_volume()
        speak("Volume muted.")

    elif "unmute" in command:
        unmute_volume()
        speak("Volume unmuted.")

    elif "volume up" in command:
        volume_up()
        speak("Volume increased.")

    elif "volume down" in command:
        volume_down()
        speak("Volume decreased.")

    elif "lock screen" or "lockdown" in command:
        speak("Locking the screen.")
        lock_screen()

    elif "shutdown" in command:
        speak("Shutting down.")
        shutdown()

    elif "restart" in command:
        speak("Restarting.")
        restart()
        
    elif "report for duty" in command:
        speak("Jarvis, at your service, sir.")

    else:
        speak("Sory, I don't understand that yet.")


def main():
    speak("Jarvis online.")
    while True:
        query = listen_command()
        if any(wake in query for wake in WAKE_WORDS):
            command = query
            for wake in WAKE_WORDS:
                command = command.replace(wake, '').strip()
            if command:
                handle_command(command)

if __name__ == "__main__":
    main()