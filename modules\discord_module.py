import discum
import threading

# ⚠️ Your Discord user token goes here (DO NOT share this with anyone!)
USER_TOKEN = "YOUR_USER_TOKEN_HERE"

def send_discord_message(user_name: str, message: str):
    bot = discum.Client(token=USER_TOKEN, log=False)

    def start_bot():
        @bot.gateway.command
        def on_ready(resp):
            if resp.event.ready_supplemental:
                print("[<PERSON>] Logged in.")
                user_found = False
                for guild_id in bot.gateway.session.guild_ids:
                    members = bot.getGuildMembers(guild_id, channelID=None, presences=False)
                    bot.gateway.fetchMembers(guild_id, keep="all", wait=1)
                    bot.gateway.run(auto_reconnect=False)  # Pause and allow member fetch

                    for m in bot.gateway.session.guild(guild_id).members:
                        username = m["username"]
                        if username.lower() == user_name.lower():
                            user_id = m["userId"]
                            print(f"[<PERSON>] Found user: {username} ({user_id})")
                            bot.sendMessage(user_id, message)
                            user_found = True
                            break
                    if user_found:
                        break

                if not user_found:
                    print("[<PERSON>] User not found.")
                bot.gateway.close()

        bot.gateway.run()

    thread = threading.Thread(target=start_bot)
    thread.start()
